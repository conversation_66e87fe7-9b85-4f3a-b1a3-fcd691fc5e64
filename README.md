# Civility Chat

A civil co-parenting chat app with AI-powered content moderation to help divorced or separated parents communicate respectfully about child-related matters.

## Overview

Civility provides a secure, end-to-end encrypted chat experience that automatically detects and manages toxic messages using Google's Gemini AI. The app helps maintain civil communication between co-parents while protecting users from abusive or manipulative language.

## Key Features

- **AI Content Moderation**: Real-time message analysis using Gemini 2.5 Flash-Lite
- **End-to-End Encryption**: Signal protocol implementation for secure messaging
- **Smart Message Handling**: Block, warn, or allow messages based on AI analysis
- **Clean Interface**: Built with React Native Gifted Chat for familiar UX
- **Push Notifications**: Expo notifications for timely message delivery

## Tech Stack

- **Mobile**: Expo React Native (TypeScript)
- **Backend**: Firebase Cloud Functions, Firestore
- **AI Moderation**: Google Gemini 2.5 Flash-Lite
- **Authentication**: Firebase Auth
- **Encryption**: libsignal-protocol-js with Expo Secure Store
- **UI**: react-native-gifted-chat, React Navigation

## Development Status

🚧 **Currently in MVP development phase**

This project is being built following a structured implementation plan with the following phases:

- **Phase 0**: Tooling & Skeleton ⏳
- **Phase 1**: Core Chat Flow
- **Phase 2**: UX Polish  
- **Phase 3**: Notifications & Telemetry
- **Phase 4**: Hardening & DX
- **Phase 5**: Pilot & Feedback

## Getting Started

### Prerequisites

- Node.js 18+
- Expo CLI
- Firebase project
- Google Cloud project with Vertex AI enabled

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd civility-chat

# Install dependencies
npm install

# Start the development server
npx expo start
```

### Configuration

1. Create a Firebase project at https://console.firebase.google.com
2. Enable Authentication, Firestore, and Cloud Functions
3. Copy your Firebase config to `env.ts`
4. Set up Google Cloud Vertex AI for Gemini access

## Project Structure

```
civility-chat/
├── src/
│   ├── components/     # Reusable UI components
│   ├── screens/        # App screens
│   ├── hooks/          # Custom React hooks
│   ├── services/       # API and Firebase services
│   └── types/          # TypeScript type definitions
├── functions/          # Firebase Cloud Functions
├── memory-bank/        # Project documentation
└── README.md
```

## Contributing

This is currently a private MVP development project. Please refer to the implementation plan in `memory-bank/implementation-plan.md` for development guidelines.

## License

Private project - All rights reserved.

---

**Codename**: Civility  
**Target**: Co-parenting communication platform  
**Status**: MVP Development  
**Last Updated**: 2025-07-09
