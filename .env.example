# Civility Chat App - Environment Variables Template
# Copy this file to .env and fill in your actual values

# Firebase Configuration
# Get these values from your Firebase project settings
EXPO_PUBLIC_FIREBASE_API_KEY=your-api-key-here
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project-id.firebaseapp.com
EXPO_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project-id.appspot.com
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789
EXPO_PUBLIC_FIREBASE_APP_ID=1:123456789:web:abcdef123456789

# Google Cloud / Gemini API
# Get this from Google Cloud Console with Vertex AI enabled
EXPO_PUBLIC_GEMINI_API_KEY=your-gemini-api-key-here

# Development Settings
EXPO_PUBLIC_DEBUG_MODE=true
