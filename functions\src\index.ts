// Firebase Cloud Functions for Civility Chat
// This file contains the main Cloud Functions for message moderation

import { onDocumentCreated } from 'firebase-functions/v2/firestore';
import { initializeApp } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize Firebase Admin
initializeApp();
const db = getFirestore();

// Initialize Gemini AI (API key should be set in Firebase Functions config)
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '');

/**
 * Moderate message content using Gemini AI
 * Triggered when a new message is created in Firestore
 */
export const moderateMessage = onDocumentCreated(
  'chats/{threadId}/messages/{messageId}',
  async (event) => {
    const snap = event.data;
    if (!snap) {
      console.log('No data associated with the event');
      return;
    }

    const data = snap.data();
    const { text, senderId, timestamp } = data;

    // Skip if message is already moderated
    if (data.moderated) {
      console.log('Message already moderated, skipping');
      return;
    }

    try {
      console.log(`Moderating message from ${senderId}: "${text}"`);
      
      // Call Gemini for content moderation
      const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
      
      const prompt = `
        Analyze this message for toxic, abusive, or inappropriate content in the context of co-parenting communication.
        
        Message: "${text}"
        
        Respond with a JSON object containing:
        - decision: "allow", "warn", or "block"
        - confidence: number between 0-1
        - labels: array of detected issues (e.g., ["hostile", "threatening"])
        - reason: brief explanation
        
        Guidelines:
        - "allow": Normal, civil communication
        - "warn": Potentially problematic but not clearly abusive
        - "block": Clearly toxic, abusive, or inappropriate
        
        Focus on protecting civil co-parenting communication.
      `;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const moderationResult = JSON.parse(response.text());

      // Update the message with moderation results
      await snap.ref.update({
        moderated: true,
        moderation: {
          decision: moderationResult.decision,
          confidence: moderationResult.confidence,
          labels: moderationResult.labels || [],
          reason: moderationResult.reason,
          timestamp: new Date().toISOString(),
        },
        // If blocked, replace the text
        ...(moderationResult.decision === 'block' && {
          originalText: text,
          text: '🛑 Message removed by moderation',
          flagged: true,
        }),
        // If warned, add a flag
        ...(moderationResult.decision === 'warn' && {
          warned: true,
        }),
      });

      console.log(`Moderation complete: ${moderationResult.decision} (confidence: ${moderationResult.confidence})`);

    } catch (error) {
      console.error('Error moderating message:', error);
      
      // Mark as moderated with error to prevent retry loops
      await snap.ref.update({
        moderated: true,
        moderation: {
          decision: 'allow', // Default to allow on error
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString(),
        },
      });
    }
  }
);

/**
 * Health check function for monitoring
 */
export const healthCheck = onDocumentCreated(
  'health/{checkId}',
  async (event) => {
    console.log('Health check triggered');
    return { status: 'ok', timestamp: new Date().toISOString() };
  }
);
