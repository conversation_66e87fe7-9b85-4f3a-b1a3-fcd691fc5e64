{"dependencies": {"@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "expo": "~53.0.19", "expo-secure-store": "~14.2.3", "expo-status-bar": "~2.2.3", "firebase": "^11.10.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "~0.20.0", "react-test-renderer": "19.0.0", "react-native-gifted-chat": "2.6.5", "react-native-get-random-values": "~1.11.0", "@expo/metro-runtime": "~5.0.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "@types/jest": "^30.0.0", "@types/react": "~19.0.10", "jest": "29.7.0", "jest-expo": "^53.0.9", "typescript": "~5.8.3"}}