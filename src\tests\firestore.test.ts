// Simple test checklist for Firestore schema validation
// Run this manually to validate Firestore setup

import { 
  createUser, 
  getUser, 
  createChatThread, 
  getChatThread,
  sendMessage,
  getThreadMessages,
  subscribeToThreadMessages,
  subscribeToUserThreads
} from '../services/firestore';

// Test checklist - run these manually once Firebase is configured
export const firestoreTestChecklist = {
  
  // Test 1: User operations
  testUserOperations: async () => {
    console.log('🧪 Testing user operations...');
    
    const testUser = {
      uid: 'test-user-123',
      email: '<EMAIL>',
      displayName: 'Test User'
    };
    
    try {
      // Create user
      await createUser(testUser);
      console.log('✅ User created successfully');
      
      // Get user
      const retrievedUser = await getUser(testUser.uid);
      console.log('✅ User retrieved:', retrievedUser);
      
      return true;
    } catch (error) {
      console.error('❌ User operations failed:', error);
      return false;
    }
  },
  
  // Test 2: Chat thread operations
  testChatThreadOperations: async () => {
    console.log('🧪 Testing chat thread operations...');
    
    try {
      // Create thread
      const threadId = await createChatThread(['user1', 'user2']);
      console.log('✅ Thread created:', threadId);
      
      // Get thread
      const thread = await getChatThread(threadId);
      console.log('✅ Thread retrieved:', thread);
      
      return { success: true, threadId };
    } catch (error) {
      console.error('❌ Chat thread operations failed:', error);
      return { success: false, threadId: null };
    }
  },
  
  // Test 3: Message operations
  testMessageOperations: async (threadId: string) => {
    console.log('🧪 Testing message operations...');
    
    try {
      // Send message
      const messageId = await sendMessage(threadId, 'user1', 'Hello, world!');
      console.log('✅ Message sent:', messageId);
      
      // Get messages
      const messages = await getThreadMessages(threadId);
      console.log('✅ Messages retrieved:', messages);
      
      return true;
    } catch (error) {
      console.error('❌ Message operations failed:', error);
      return false;
    }
  },
  
  // Test 4: Real-time subscriptions
  testRealtimeSubscriptions: (threadId: string) => {
    console.log('🧪 Testing real-time subscriptions...');
    
    try {
      // Subscribe to messages
      const unsubscribeMessages = subscribeToThreadMessages(threadId, (messages) => {
        console.log('📨 Real-time messages update:', messages.length, 'messages');
      });
      
      // Subscribe to threads
      const unsubscribeThreads = subscribeToUserThreads('user1', (threads) => {
        console.log('📋 Real-time threads update:', threads.length, 'threads');
      });
      
      console.log('✅ Real-time subscriptions set up');
      
      // Return cleanup function
      return () => {
        unsubscribeMessages();
        unsubscribeThreads();
        console.log('🧹 Real-time subscriptions cleaned up');
      };
    } catch (error) {
      console.error('❌ Real-time subscriptions failed:', error);
      return null;
    }
  },
  
  // Run all tests
  runAllTests: async () => {
    console.log('🚀 Starting Firestore schema validation...');
    
    // Test 1: Users
    const userTest = await firestoreTestChecklist.testUserOperations();
    if (!userTest) return false;
    
    // Test 2: Threads
    const threadTest = await firestoreTestChecklist.testChatThreadOperations();
    if (!threadTest.success) return false;
    
    // Test 3: Messages
    const messageTest = await firestoreTestChecklist.testMessageOperations(threadTest.threadId!);
    if (!messageTest) return false;
    
    // Test 4: Real-time
    const cleanup = firestoreTestChecklist.testRealtimeSubscriptions(threadTest.threadId!);
    if (!cleanup) return false;
    
    console.log('✅ All Firestore schema tests passed!');
    
    // Clean up after 5 seconds
    setTimeout(() => {
      cleanup();
    }, 5000);
    
    return true;
  }
};

// Export for manual testing
export default firestoreTestChecklist;
