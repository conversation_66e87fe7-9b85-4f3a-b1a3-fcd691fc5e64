// Firestore service functions for chat operations
import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDoc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  limit, 
  onSnapshot,
  serverTimestamp,
  Timestamp,
  DocumentReference,
  QuerySnapshot,
  DocumentSnapshot,
  setDoc
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { 
  User, 
  ChatThread, 
  Message, 
  FirestoreUser, 
  FirestoreChatThread, 
  FirestoreMessage 
} from '../types/chat';

// Collection references
const usersCollection = collection(db, 'users');
const threadsCollection = collection(db, 'threads');

// Helper function to convert Firestore timestamp to ISO string
const timestampToString = (timestamp: any): string => {
  if (timestamp?.toDate) {
    return timestamp.toDate().toISOString();
  }
  if (timestamp instanceof Date) {
    return timestamp.toISOString();
  }
  return timestamp || new Date().toISOString();
};

// User operations
export const createUser = async (user: Omit<User, 'createdAt' | 'lastSeen'>): Promise<void> => {
  const userDoc = doc(usersCollection, user.uid);
  const userData: FirestoreUser = {
    email: user.email,
    displayName: user.displayName,
    avatar: user.avatar,
    createdAt: serverTimestamp(),
    lastSeen: serverTimestamp(),
  };
  
  await setDoc(userDoc, userData, { merge: true });
};

export const getUser = async (uid: string): Promise<User | null> => {
  const userDoc = doc(usersCollection, uid);
  const snapshot = await getDoc(userDoc);
  
  if (!snapshot.exists()) {
    return null;
  }
  
  const data = snapshot.data() as FirestoreUser;
  return {
    uid,
    email: data.email,
    displayName: data.displayName,
    avatar: data.avatar,
    createdAt: timestampToString(data.createdAt),
    lastSeen: timestampToString(data.lastSeen),
  };
};

export const updateUserLastSeen = async (uid: string): Promise<void> => {
  const userDoc = doc(usersCollection, uid);
  await updateDoc(userDoc, {
    lastSeen: serverTimestamp(),
  });
};

export const getUserByEmail = async (email: string): Promise<User | null> => {
  const q = query(usersCollection, where('email', '==', email));
  const snapshot = await getDocs(q);
  if (snapshot.empty) return null;
  const docSnap = snapshot.docs[0];
  const data = docSnap.data() as FirestoreUser;
  return {
    uid: docSnap.id,
    email: data.email,
    displayName: data.displayName,
    avatar: data.avatar,
    createdAt: timestampToString(data.createdAt),
    lastSeen: timestampToString(data.lastSeen),
  };
};

// Chat thread operations
export const createChatThread = async (participants: string[]): Promise<string> => {
  const threadData: FirestoreChatThread = {
    participants,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp(),
  };
  
  const docRef = await addDoc(threadsCollection, threadData);
  return docRef.id;
};

export const getChatThread = async (threadId: string): Promise<ChatThread | null> => {
  const threadDoc = doc(threadsCollection, threadId);
  const snapshot = await getDoc(threadDoc);
  
  if (!snapshot.exists()) {
    return null;
  }
  
  const data = snapshot.data() as FirestoreChatThread;
  return {
    id: threadId,
    participants: data.participants,
    createdAt: timestampToString(data.createdAt),
    updatedAt: timestampToString(data.updatedAt),
    lastMessage: data.lastMessage ? {
      ...data.lastMessage,
      timestamp: timestampToString(data.lastMessage.timestamp),
    } : undefined,
  };
};

export const getUserChatThreads = async (uid: string): Promise<ChatThread[]> => {
  const q = query(
    threadsCollection,
    where('participants', 'array-contains', uid),
    orderBy('updatedAt', 'desc')
  );
  
  const snapshot = await getDocs(q);
  return snapshot.docs.map(doc => {
    const data = doc.data() as FirestoreChatThread;
    return {
      id: doc.id,
      participants: data.participants,
      createdAt: timestampToString(data.createdAt),
      updatedAt: timestampToString(data.updatedAt),
      lastMessage: data.lastMessage ? {
        ...data.lastMessage,
        timestamp: timestampToString(data.lastMessage.timestamp),
      } : undefined,
    };
  });
};

// Message operations

export const bufferMessage = async (
  threadId: string,
  senderId: string,
  text: string
): Promise<void> => {
  console.log('bufferMessage called with:', { threadId, senderId, text });
  try {
    const bufferCollection = collection(db, 'threads', threadId, 'buffers', senderId, 'messages');
    console.log('Adding to buffer collection at path:', bufferCollection.path);

    const docRef = await addDoc(bufferCollection, {
      text,
      timestamp: serverTimestamp(),
    });

    console.log('Message buffered successfully with ID:', docRef.id);
  } catch (error) {
    console.error('Error buffering message:', error);
    throw error;
  }
};

export const flushBuffer = async (
  threadId: string,
  userId: string
): Promise<void> => {
  console.log('flushBuffer called with:', { threadId, userId });
  try {
    // Create/overwrite the user's flush request doc inside the thread-specific subcollection
    const flushDoc = doc(db, 'threads', threadId, 'flushRequests', userId);
    console.log('Creating flush request document at path:', flushDoc.path);

    await setDoc(flushDoc, {
      threadId,
      userId,
      timestamp: serverTimestamp(),
    });

    console.log('Flush request created successfully');
  } catch (error) {
    console.error('Error creating flush request:', error);
    throw error;
  }
};

export const sendMessage = async (
  threadId: string, 
  senderId: string, 
  text: string
): Promise<string> => {
  const messagesCollection = collection(db, 'chats', threadId, 'messages');
  
  const messageData: FirestoreMessage = {
    senderId,
    text,
    timestamp: serverTimestamp(),
    delivered: false,
    read: false,
    moderated: false,    // Not yet moderated
    visible: false,      // Not visible until moderated
    status: 'sent',      // Set initial status for long messages
  };
  
  const docRef = await addDoc(messagesCollection, messageData);
  
  // DON'T update thread's lastMessage here - let moderation function handle it
  // This prevents unmoderated content from appearing in chat previews
  
  return docRef.id;
};

export const getThreadMessages = async (
  threadId: string, 
  limitCount: number = 50
): Promise<Message[]> => {
  const messagesCollection = collection(db, 'chats', threadId, 'messages');
  const q = query(
    messagesCollection,
    orderBy('timestamp', 'desc'),
    limit(limitCount)
  );
  
  const snapshot = await getDocs(q);
  return snapshot.docs.map(doc => {
    const data = doc.data() as FirestoreMessage;
    return {
      id: doc.id,
      threadId,
      senderId: data.senderId,
      text: data.text,
      timestamp: timestampToString(data.timestamp),
      moderated: data.moderated,
      visible: data.visible,        // Add the missing visible property
      moderation: data.moderation,
      originalText: data.originalText,
      flagged: data.flagged,
      warned: data.warned,
      delivered: data.delivered,
      read: data.read,
      readAt: data.readAt ? timestampToString(data.readAt) : undefined,
    };
  });
};

// Real-time subscriptions
export const subscribeToThreadMessages = (
  threadId: string,
  callback: (messages: Message[]) => void,
  limitCount: number = 50
): (() => void) => {
  const messagesCollection = collection(db, 'chats', threadId, 'messages');
  const q = query(
    messagesCollection,
    orderBy('timestamp', 'desc'),
    limit(limitCount)
  );
  
  return onSnapshot(q, (snapshot) => {
    const messages = snapshot.docs.map(doc => {
      const data = doc.data() as FirestoreMessage;
      return {
        id: doc.id,
        threadId,
        senderId: data.senderId,
        text: data.text,
        timestamp: timestampToString(data.timestamp),
        moderated: data.moderated,
        visible: data.visible,        // Add the missing visible property
        moderation: data.moderation,
        originalText: data.originalText,
        flagged: data.flagged,
        warned: data.warned,
        delivered: data.delivered,
        read: data.read,
        readAt: data.readAt ? timestampToString(data.readAt) : undefined,
      };
    });
    
    callback(messages);
  });
};

export const subscribeToUserThreads = (
  uid: string,
  callback: (threads: ChatThread[]) => void
): (() => void) => {
  const q = query(
    threadsCollection,
    where('participants', 'array-contains', uid),
    orderBy('updatedAt', 'desc')
  );
  
  return onSnapshot(q, (snapshot) => {
    const threads = snapshot.docs.map(doc => {
      const data = doc.data() as FirestoreChatThread;
      return {
        id: doc.id,
        participants: data.participants,
        createdAt: timestampToString(data.createdAt),
        updatedAt: timestampToString(data.updatedAt),
        lastMessage: data.lastMessage ? {
          ...data.lastMessage,
          timestamp: timestampToString(data.lastMessage.timestamp),
        } : undefined,
      };
    });
    
    callback(threads);
  });
};

// Message moderation operations
export const updateMessageModeration = async (
  threadId: string,
  messageId: string,
  moderation: Message['moderation'],
  flagged?: boolean,
  warned?: boolean,
  originalText?: string
): Promise<void> => {
  const messageDoc = doc(db, 'chats', threadId, 'messages', messageId);
  
  const updates: Partial<FirestoreMessage> = {
    moderated: true,
    moderation,
  };
  
  if (flagged !== undefined) updates.flagged = flagged;
  if (warned !== undefined) updates.warned = warned;
  if (originalText !== undefined) updates.originalText = originalText;
  
  await updateDoc(messageDoc, updates);
};

export const markMessageAsRead = async (
  threadId: string,
  messageId: string
): Promise<void> => {
  const messageDoc = doc(db, 'chats', threadId, 'messages', messageId);
  await updateDoc(messageDoc, {
    read: true,
    readAt: serverTimestamp(),
  });
};


