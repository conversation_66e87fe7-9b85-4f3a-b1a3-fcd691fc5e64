# Civility Chat - Phase A Test Plan

## Overview
This document outlines the manual test cases to validate Phase A of the Civility Chat moderation system, focusing on message buffering and anti-bypass features.

## 1. Basic Moderation Tests

### Test 1.1: Blocked Message
- **Action**: Send "You're an idiot!"
- **Expected**: 
  - Message shows as "🛑 Message removed by moderation"
  - Original message stored in `originalText`
  - `blocked: true` in message metadata

### Test 1.2: Warned Message
- **Action**: Send "That was a bit harsh"
- **Expected**:
  - Message shows with warning indicator
  - `warned: true` in message metadata

### Test 1.3: Allowed Message
- **Action**: Send "Can we reschedule for 3pm?"
- **Expected**:
  - Message shows normally
  - No warning/block indicators

## 2. Message Buffering Tests

### Test 2.1: Short Message Buffer
1. Send: "H"
2. Wait 5 seconds
3. Send: "i"
4. Wait 5 seconds
5. Send: "!"
6. **Verify**: After 8+ seconds, all messages combine into "H i !"
7. **Verify**: Combined message is moderated as a single unit

### Test 2.2: Buffer Flush on Long Message
1. Send: "H"
2. Send: "i"
3. Send: "How are you doing today?"
4. **Verify**: "H i" is combined and sent first
5. **Verify**: "How are you doing today?" is sent separately

## 3. Edge Cases

### Test 3.1: Empty Buffer
1. Wait 10 seconds without sending messages
2. **Verify**: No flush occurs (no empty messages sent)

### Test 3.2: Single Character Messages
1. Send: "H"
2. Wait 10 seconds
3. **Verify**: Single "H" is sent after timeout

### Test 3.3: Mixed Content
1. Send: "Y"
2. Send: "o"
3. Send: "u are a jerk"
4. **Verify**: "Y o" is combined and sent
5. **Verify**: "u are a jerk" is blocked as profanity

## 4. UI/UX Tests

### Test 4.1: Message Status
1. Send any message
2. **Verify**: Shows "✓" when sent
3. **Verify**: Shows "✓✓" when delivered (if implemented)
4. **Verify**: Shows "✗" if sending fails

### Test 4.2: Sender/Receiver Views
1. As Sender: Send a blocked message
2. As Receiver: Verify you see the blocked message placeholder
3. As Sender: Verify you can still see your original message

## 5. Performance Tests

### Test 5.1: Rapid Fire Messages
1. Quickly send: "H", "e", "y", " ", "t", "h", "e", "r", "e"
2. **Verify**: All messages are eventually combined correctly
3. **Verify**: No messages are lost

## Database Verification
After completing tests, check Firestore:

1. **Collection Structure**:
   - Messages in `chats/{threadId}/messages`
   - Thread metadata in `threads/{threadId}`

2. **Message Metadata**:
   - Moderation status properly set
   - Original text preserved for blocked messages
   - Timestamps are correct

3. **Thread Updates**:
   - `lastMessage` is updated correctly
   - `updatedAt` timestamp is updated

## Notes
- All tests should be performed with network connectivity
- Clear app cache between major test scenarios
- Verify both sender and receiver experiences
