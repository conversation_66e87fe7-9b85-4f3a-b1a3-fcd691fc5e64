# Firebase Setup Guide for Civility Chat

This guide walks you through setting up Firebase for the Civility chat application.

## Prerequisites

- Google account
- Node.js 18+ installed
- Firebase CLI installed (`npm install -g firebase-tools`)

## Step 1: Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com)
2. Click "Create a project"
3. Enter project name: `civility-chat` (or your preferred name)
4. Enable Google Analytics (recommended)
5. Choose or create a Google Analytics account
6. Click "Create project"

## Step 2: Enable Required Services

### Authentication
1. In Firebase Console, go to "Authentication"
2. Click "Get started"
3. Go to "Sign-in method" tab
4. Enable "Email/Password" provider
5. Enable "Email link (passwordless sign-in)" option

### Firestore Database
1. Go to "Firestore Database"
2. Click "Create database"
3. Choose "Start in test mode" (we'll secure it later)
4. Select a location close to your users

### Cloud Functions
1. Go to "Functions"
2. Click "Get started"
3. Follow the setup instructions

## Step 3: Get Configuration

1. Go to Project Settings (gear icon)
2. Scroll down to "Your apps"
3. Click "Add app" → Web app icon
4. Enter app nickname: "civility-chat-web"
5. Check "Also set up Firebase Hosting"
6. Click "Register app"
7. Copy the config object

## Step 4: Configure Environment

1. Copy `.env.example` to `.env`
2. Fill in the Firebase config values from Step 3
3. The config should look like:

```
EXPO_PUBLIC_FIREBASE_API_KEY=AIzaSyC...
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=civility-chat-12345.firebaseapp.com
EXPO_PUBLIC_FIREBASE_PROJECT_ID=civility-chat-12345
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=civility-chat-12345.appspot.com
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789012
EXPO_PUBLIC_FIREBASE_APP_ID=1:123456789012:web:abcdef123456
```

## Step 5: Initialize Firebase CLI

```bash
# Login to Firebase
firebase login

# Initialize Firebase in your project
firebase init

# Select:
# - Functions: Configure and deploy Cloud Functions
# - Firestore: Deploy rules and create indexes
# - Hosting: Configure and deploy Firebase Hosting sites
```

## Step 6: Test Connection

Run the app and check that Firebase initializes without errors:

```bash
npx expo start
```

## Next Steps

- Set up Firestore security rules
- Deploy Cloud Functions for moderation
- Configure Gemini API access
- Set up push notifications

## Troubleshooting

### Common Issues

1. **"Firebase config not found"**: Check that `.env` file exists and has correct values
2. **"Permission denied"**: Ensure Firestore rules allow read/write for authenticated users
3. **"Functions not deploying"**: Check that billing is enabled for Cloud Functions

### Support

- [Firebase Documentation](https://firebase.google.com/docs)
- [Expo Firebase Guide](https://docs.expo.dev/guides/using-firebase/)
