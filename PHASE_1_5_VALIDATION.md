Is there any configuration I need to do by hand like get a GEMINI_API_KEY and add it to .env? │
╰───────────────────────────────────────────────────────────────────────────────────────────────────╯

✦ Yes, that's a great question. You will need to get a GEMINI_API_KEY and make it available to the Cloud Function. Here's how you can do that:


   1. Obtain your API key: Go to Google AI Studio to generate your API key.
   2. Create a `.env` file: You'll need to create a .env file inside the functions directory. This file will store your API key securely.
   3. Add the key to the `.env` file: Add the following line to the functions/.env file, replacing "YOUR_API_KEY_HERE" with the key you just generated:
      GEMINI_API_KEY="YOUR_API_KEY_HERE"

  After you've created the functions/.env file with your API key, you'll need to deploy the function to Firebase. You can do this by running the following command from the root of your
  project:


  firebase deploy --only functions

  Once the function is deployed, it will automatically moderate new messages as they are created in Firestore.