# Phase 1.5 Testing Guide: moderateMessage Cloud Function

## Overview
This guide provides comprehensive testing instructions for the enhanced `moderateMessage` Cloud Function that integrates with Gemini 2.5 Flash-Lite for AI-powered message moderation.

## What Was Implemented

### Enhanced Features
- **Gemini 2.5 Flash-Lite Integration**: Uses `gemini-1.5-flash` model for speed and cost efficiency
- **Structured JSON Responses**: Parses structured moderation decisions with labels and confidence scores
- **Co-parenting Specific Prompting**: Tailored prompts for co-parenting communication scenarios
- **Comprehensive Logging**: Detailed performance and decision logging for debugging
- **Error Handling**: Robust fallback mechanisms that "fail open" for safety
- **Audit Trail**: SHA-256 message hashing for compliance without storing plaintext
- **Performance Monitoring**: Latency tracking with 400ms target alerts

### Moderation Decisions
- **ALLOW**: Professional, neutral, or positive child-related communication
- **WARN**: Mild hostility, passive-aggressive language, inappropriate tone
- **BLOCK**: Threats, severe abuse, harassment, profanity, manipulation

## Prerequisites

### 1. Environment Setup
Ensure your `.env` file in `civility-functions/` contains:
```bash
GEMINI_API_KEY=your_actual_gemini_api_key_here
```

### 2. Firebase Project Configuration
- Firestore database should be initialized
- Cloud Functions should be deployed (already done)
- Gemini API should be enabled in Google Cloud Console

## Testing Scenarios

### Test 1: Clean Message (ALLOW)
**Purpose**: Verify normal messages pass through without modification

**Steps**:
1. Open your Civility chat app
2. Send a clean, professional message like:
   ```
   "Can we schedule pickup for Saturday at 3pm? Emma has soccer practice at 4."
   ```

**Expected Results**:
- Message appears normally in chat
- In Firestore console, check the message document should have:
  - `moderated: true`
  - `decision: "allow"`
  - `labels: ["clean"]` (or similar)
  - `confidence: 0.8+`
  - `latency: <400ms`
  - Original `text` unchanged

### Test 2: Mildly Hostile Message (WARN)
**Purpose**: Test warning system for borderline content

**Steps**:
1. Send a passive-aggressive message like:
   ```
   "I guess you're too busy to respond to my texts about our child again."
   ```

**Expected Results**:
- Message appears in chat (not blocked)
- In Firestore console, message document should have:
  - `moderated: true`
  - `decision: "warn"`
  - `warned: true`
  - `labels: ["passive-aggressive", "inappropriate-tone"]` (or similar)
  - `confidence: 0.6+`
  - Original `text` unchanged

### Test 3: Toxic Message (BLOCK)
**Purpose**: Verify blocking of clearly inappropriate content

**Steps**:
1. Send a clearly inappropriate message like:
   ```
   "You're a terrible parent and I hate dealing with you."
   ```

**Expected Results**:
- Message text is replaced with: `"🛑 Message removed by moderation"`
- In Firestore console, message document should have:
  - `moderated: true`
  - `decision: "block"`
  - `blocked: true`
  - `labels: ["personal-attack", "abuse"]` (or similar)
  - `confidence: 0.8+`
  - `text: "🛑 Message removed by moderation"`
  - `originalTextHash: [sha256 hash]`

### Test 4: Performance Testing
**Purpose**: Verify latency meets <400ms target

**Steps**:
1. Send several messages in quick succession
2. Monitor Firebase Functions logs

**Expected Results**:
- Check logs for latency measurements
- Most messages should complete in <400ms
- Any >400ms should trigger warning logs

### Test 5: Error Handling
**Purpose**: Test graceful failure modes

**Steps**:
1. Temporarily set invalid `GEMINI_API_KEY` in function environment
2. Send a test message
3. Restore correct API key

**Expected Results**:
- Message should still appear (fail-open behavior)
- Firestore document should have:
  - `moderated: true`
  - `decision: "allow"`
  - `error: [error message]`

## Monitoring and Validation

### Firebase Console Checks

1. **Firestore Database**:
   - Navigate to `chats/{threadId}/messages/{msgId}`
   - Verify moderation fields are populated correctly
   - Check that `messageHash` is present for audit trail

2. **Cloud Functions Logs**:
   - Go to Firebase Console > Functions > moderateMessage
   - Click "Logs" to see detailed execution logs
   - Look for performance metrics and any errors

3. **Function Metrics**:
   - Check invocation count
   - Monitor execution time
   - Watch for error rates

### Key Metrics to Monitor

- **Latency**: Should be <400ms for 95th percentile
- **Success Rate**: Should be >99%
- **Cost**: Track Gemini API usage (should be <$0.50 per 100k messages)
- **Decision Distribution**: Monitor allow/warn/block ratios

## Troubleshooting

### Common Issues

1. **Function Not Triggering**:
   - Check Firestore rules allow writes
   - Verify message document structure includes `text` field
   - Ensure user is authenticated

2. **Gemini API Errors**:
   - Verify `GEMINI_API_KEY` is set correctly
   - Check Google Cloud Console for API quotas
   - Ensure Vertex AI API is enabled

3. **High Latency**:
   - Check network connectivity
   - Monitor Gemini API response times
   - Consider regional deployment closer to users

4. **Parsing Errors**:
   - Check function logs for JSON parsing issues
   - Gemini responses should be valid JSON
   - Fallback mechanism should handle malformed responses

## Success Criteria for Phase 1.5

✅ **Functional Requirements**:
- [ ] Messages trigger moderation automatically
- [ ] Clean messages pass through unchanged
- [ ] Warned messages display with original text
- [ ] Blocked messages show placeholder text
- [ ] All decisions logged with audit trail

✅ **Performance Requirements**:
- [ ] 95th percentile latency <400ms
- [ ] Function success rate >99%
- [ ] Cost per message <$0.005

✅ **Security Requirements**:
- [ ] Original text hashed for blocked messages
- [ ] No plaintext stored beyond 30 days (future requirement)
- [ ] Proper error handling prevents data leaks

## Next Steps

After successful testing:
1. Document any issues or edge cases discovered
2. Collect performance metrics over 24-48 hours
3. Ready to proceed to **Phase 1.6**: UI enhancements for blocked/warned messages
4. Consider A/B testing different moderation thresholds

## Test Data Suggestions

### Safe Messages (Should ALLOW)
- "Emma's recital is next Friday at 7pm"
- "Can you pick up the kids early today?"
- "Thanks for handling the doctor appointment"
- "School called about the field trip permission slip"

### Borderline Messages (Should WARN)
- "You always forget to pack her lunch"
- "I guess punctuality isn't your strong suit"
- "Fine, whatever works for you"
- "You never listen to what I'm saying"

### Toxic Messages (Should BLOCK)
- "You're a horrible parent"
- "I can't stand dealing with you"
- "The kids would be better off without you"
- "You make me sick"

---

**Important**: Test with real message flows in your app to ensure the full integration works end-to-end. Monitor logs closely during initial testing to catch any edge cases.
