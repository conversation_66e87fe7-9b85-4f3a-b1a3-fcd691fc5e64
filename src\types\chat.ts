// Type definitions for chat-related data structures

export interface User {
  uid: string;
  email: string;
  displayName?: string;
  avatar?: string;
  createdAt: string;
  lastSeen: string;
}

export interface ChatThread {
  id: string;
  participants: string[]; // Array of user UIDs
  createdAt: string;
  updatedAt: string;
  lastMessage?: {
    text: string;
    senderId: string;
    timestamp: string;
  };
}

export interface Message {
  id: string;
  threadId: string;
  senderId: string;
  text: string;
  timestamp: string;
  
  // Moderation fields
  moderated?: boolean;
  moderation?: {
    decision: 'allow' | 'warn' | 'block';
    confidence: number;
    labels: string[];
    reason: string;
    timestamp: string;
    error?: string;
  };
  
  // Content modification fields
  originalText?: string; // Stored when message is blocked/modified
  flagged?: boolean;     // True when message is blocked
  warned?: boolean;      // True when message has warning
  
  // Message status
  delivered?: boolean;
  read?: boolean;
  readAt?: string;
}

export interface ModerationResult {
  decision: 'allow' | 'warn' | 'block';
  confidence: number;
  labels: string[];
  reason: string;
}

// Firestore document interfaces (for type safety)
export interface FirestoreUser extends Omit<User, 'uid' | 'createdAt' | 'lastSeen'> {
  // uid is the document ID in Firestore
  createdAt: any; // FieldValue or Timestamp
  lastSeen: any; // FieldValue or Timestamp
}

export interface FirestoreChatThread extends Omit<ChatThread, 'id' | 'createdAt' | 'updatedAt' | 'lastMessage'> {
  // id is the document ID in Firestore
  createdAt: any; // FieldValue or Timestamp
  updatedAt: any; // FieldValue or Timestamp
  lastMessage?: {
    text: string;
    senderId: string;
    timestamp: any; // FieldValue or Timestamp
  };
}

export interface FirestoreMessage extends Omit<Message, 'id' | 'threadId' | 'timestamp' | 'readAt'> {
  // id and threadId are derived from document path
  timestamp: any; // FieldValue or Timestamp
  readAt?: any; // FieldValue or Timestamp
}
