import {onDocumentCreated} from "firebase-functions/v2/firestore";
import {GoogleGenerativeAI} from "@google/generative-ai";
import {logger} from "firebase-functions";
import * as crypto from "crypto";
import * as admin from "firebase-admin";

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  admin.initializeApp();
}

// Interface for moderation response
interface ModerationResponse {
  decision: "allow" | "warn" | "block";
  labels: string[];
  confidence: number;
  reasoning?: string;
}

// Co-parenting specific moderation prompt
const MODERATION_PROMPT = `
You are a content moderator for a co-parenting communication app. 
Your job is to analyze messages between divorced or separated parents 
and determine if they contain toxic, abusive, or inappropriate content.

Analyze the following message and respond with ONLY a valid JSON object 
in this exact format:
{
  "decision": "allow" | "warn" | "block",
  "labels": ["label1", "label2"],
  "confidence": 0.0-1.0,
  "reasoning": "brief explanation"
}

Decision criteria:
- "block": Contains threats, severe abuse, harassment, profanity (explicit swear words like fuck, shit, asshole, bitch, etc.) directed at person, manipulation tactics
- "warn": Contains mild hostility, passive-aggressive language, inappropriate tone, name-calling or minor insults (e.g. "idiot", "dirtbag", "jerk")
- "allow": Professional, neutral, or positive communication about child-related matters

Label guidelines:
• Use "profanity" only when the text includes explicit swear words (fuck, shit, asshole, bitch, etc.)
• If the text contains insults or name-calling but no swear words, use "hostility" or "personal-attack" (not "profanity")
• Examples: "idiot", "dirtbag", "jerk", "loser" = "hostility" or "personal-attack", NOT "profanity"

Possible labels: ["threat", "abuse", "harassment", "profanity", 
"manipulation", "hostility", "passive-aggressive", "inappropriate-tone", 
"personal-attack", "clean"]

Message to analyze: "{MESSAGE_TEXT}"
`;

/**
 * Calls the Gemini API to moderate a given text.
 * @param {string} text The text to moderate.
 * @return {Promise<ModerationResponse>} The moderation result.
 */
async function getModerationResult(text: string): Promise<ModerationResponse> {
  const genAI =
    new GoogleGenerativeAI(process.env.GEMINI_API_KEY as string);
  const model = genAI.getGenerativeModel({
    model: "gemini-1.5-flash-latest",
    generationConfig: {
      temperature: 0.1,
      topK: 1,
      topP: 0.8,
      maxOutputTokens: 200,
      responseMimeType: "application/json",
    },
  });

  const prompt = MODERATION_PROMPT.replace("{MESSAGE_TEXT}", text);

  try {
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const responseText = response.text();
    const moderationResult = JSON.parse(responseText) as ModerationResponse;

    // Validate response structure
    if (
      !moderationResult.decision ||
      !["allow", "warn", "block"].includes(moderationResult.decision)
    ) {
      logger.error("Invalid moderation decision from API", {
        decision: moderationResult.decision,
      });
      moderationResult.decision = "allow"; // Safe fallback
    }
    return moderationResult;
  } catch (error) {
    logger.error("Failed to get or parse Gemini response", {
      error,
    });
    // Fallback to allow if API call or parsing fails
    return {
      decision: "allow",
      labels: ["moderation-error"],
      confidence: 0.0,
      reasoning: "Failed to get or parse moderation response",
    };
  }
}

export const moderateMessage = onDocumentCreated(
  {
    document: "chats/{threadId}/messages/{msgId}",
    timeoutSeconds: 10,
    memory: "256MiB",
    region: "northamerica-northeast1",
  },
  async (event) => {
    const startTime = Date.now();
    const snap = event.data;

    if (!snap) {
      logger.error("No document snapshot provided");
      return;
    }

    const messageData = snap.data();
    const {text, senderId} = messageData;

    if (!text || typeof text !== "string") {
      logger.warn("No text content to moderate", {messageId: snap.id});
      return;
    }

    // Skip if already moderated
    if (messageData.moderated) {
      logger.info("Message already moderated", {messageId: snap.id});
      return;
    }

    logger.info("Starting moderation", {
      messageId: snap.id,
      textLength: text.length,
      senderId,
    });

    try {
      const moderationResult = await getModerationResult(text);

      logger.info("Moderation result received", {
        messageId: snap.id,
        decision: moderationResult.decision,
        latency: Date.now() - startTime,
      });

      // Create message hash for audit trail (without storing plaintext)
      const messageHash =
        crypto.createHash("sha256").update(text).digest("hex");

      // Prepare update data with structured moderation object
      const updateData: Record<string, unknown> = {
        moderated: true,
        moderatedAt: new Date(),
        messageHash,
        latency: Date.now() - startTime,
        visible: true, // Always visible - UI handles display based on moderation decision
        moderation: {
          decision: moderationResult.decision,
          confidence: moderationResult.confidence || 0.0,
          labels: moderationResult.labels || [],
          reason: moderationResult.reasoning || "AI moderation decision",
          timestamp: new Date().toISOString(),
        },
      };

      // Handle blocked messages
      if (moderationResult.decision === "block") {
        updateData.originalText = text; // Store original text
        updateData.text = "🛑 Message removed by moderation";
        updateData.flagged = true;
        updateData.blocked = true;
        logger.warn("Message blocked", {
          messageId: snap.id,
          labels: moderationResult.labels,
          confidence: moderationResult.confidence,
        });
      } else if (moderationResult.decision === "warn") {
        updateData.warned = true;
        logger.info("Message warned", {
          messageId: snap.id,
          labels: moderationResult.labels,
          confidence: moderationResult.confidence,
        });
      }

      // Add reasoning if provided (for debugging)
      if (moderationResult.reasoning) {
        updateData.reasoning = moderationResult.reasoning;
      }

      // Update the document
      await snap.ref.update(updateData);

      // Update thread's lastMessage only for non-blocked messages
      const threadId = snap.ref.parent.parent?.id;
      const shouldUpdateThread = moderationResult.decision !== "block"; // Skip blocked messages in thread preview

      logger.info("Attempting to update thread lastMessage", {
        messageId: snap.id,
        threadId,
        decision: moderationResult.decision,
        shouldUpdateThread,
      });

      if (threadId && shouldUpdateThread) {
        try {
          const db = admin.firestore();
          const threadRef = db.doc(`threads/${threadId}`);

          // Check if thread document exists first
          const threadDoc = await threadRef.get();
          if (!threadDoc.exists) {
            // Create thread document if it doesn't exist
            logger.info("Creating missing thread document", {
              messageId: snap.id,
              threadId,
            });

            await threadRef.set({
              participants: [messageData.senderId], // Will need to add other participant
              createdAt: admin.firestore.FieldValue.serverTimestamp(),
              updatedAt: admin.firestore.FieldValue.serverTimestamp(),
              lastMessage: {
                text: text, // Use original text for thread preview
                senderId: messageData.senderId,
                timestamp: messageData.timestamp,
                visible: true,
              },
            });
          } else {
            // Update existing thread
            await threadRef.update({
              lastMessage: {
                text: text, // Use original text for thread preview
                senderId: messageData.senderId,
                timestamp: messageData.timestamp,
                visible: true,
              },
              updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            });
          }

          logger.info("Successfully updated thread lastMessage", {
            messageId: snap.id,
            threadId,
            created: !threadDoc.exists,
          });
        } catch (threadUpdateError) {
          logger.error("Failed to update thread lastMessage", {
            messageId: snap.id,
            threadId,
            error: threadUpdateError instanceof Error ? threadUpdateError.message : String(threadUpdateError),
          });
        }
      } else if (threadId && !shouldUpdateThread) {
        logger.info("Skipping thread lastMessage update for blocked message", {
          messageId: snap.id,
          threadId,
          decision: moderationResult.decision,
        });
      }

      const totalLatency = Date.now() - startTime;
      logger.info("Moderation completed", {
        messageId: snap.id,
        decision: moderationResult.decision,
        totalLatency,
        success: true,
      });

      // Log performance metrics
      if (totalLatency > 400) {
        logger.warn("Moderation latency exceeded target", {
          messageId: snap.id,
          latency: totalLatency,
          target: 400,
        });
      }
    } catch (error) {
      const totalLatency = Date.now() - startTime;
      logger.error("Moderation failed", {
        messageId: snap.id,
        error: error instanceof Error ? error.message : String(error),
        latency: totalLatency,
      });

      // Update with error state but don't block the message
      await snap.ref.update({
        moderated: true,
        moderatedAt: new Date(),
        decision: "allow", // Fail open for safety
        error: error instanceof Error ? error.message : String(error),
        latency: totalLatency,
      });
    }
  },
);

export const processMessageBuffer = onDocumentCreated(
  {
    document: "threads/{threadId}/flushRequests/{userId}",
    timeoutSeconds: 60,
    memory: "256MiB",
    region: "northamerica-northeast1",
  },
  async (event) => {
    const startTime = Date.now();
    const snap = event.data;
    if (!snap) {
      logger.error("No flush request data provided");
      return;
    }

    const {threadId, userId} = event.params;
    logger.info("Processing message buffer flush request", {
      threadId,
      userId,
    });

    const db = admin.firestore();
    const bufferRef = db.collection(`chats/${threadId}/buffers/${userId}/messages`);
    const bufferSnapshot = await bufferRef.orderBy("timestamp").get();

    if (bufferSnapshot.empty) {
      logger.warn("Buffer is empty, deleting flush request.", {threadId, userId});
      await snap.ref.delete();
      return;
    }

    // Concatenate messages
    const combinedText = bufferSnapshot.docs
      .map((doc) => doc.data().text)
      .join(" ")
      .trim();

    if (!combinedText) {
      logger.warn("Buffer contained only empty messages.", {threadId, userId});
      // Clean up buffer and request even if text is empty
      const batch = db.batch();
      bufferSnapshot.docs.forEach((doc) => {
        batch.delete(doc.ref);
      });
      batch.delete(snap.ref);
      await batch.commit();
      return;
    }

    // Moderate the combined text
    const moderationResult = await getModerationResult(combinedText);

    // Create a new message in the main chat thread
    const mainMessagesRef = db.collection(`chats/${threadId}/messages`);
    const newMessageRef = mainMessagesRef.doc();

    const newMessageData: Record<string, unknown> = {
      text: combinedText,
      senderId: userId,
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      moderated: true,
      moderatedAt: new Date(),
      visible: true, // UI will handle visibility based on moderation
      status: "sent",
      moderation: {
        decision: moderationResult.decision,
        confidence: moderationResult.confidence || 0.0,
        labels: moderationResult.labels || [],
        reason: moderationResult.reasoning || "AI moderation decision",
        timestamp: new Date().toISOString(),
        source: "buffer",
      },
    };

    // Handle blocked messages from buffer
    if (moderationResult.decision === "block") {
      newMessageData.originalText = combinedText;
      newMessageData.text = "🛑 Message removed by moderation";
      newMessageData.flagged = true;
      newMessageData.blocked = true;
    }

    // First, save the new message
    await newMessageRef.set(newMessageData);

    // Only update thread lastMessage for non-blocked messages
    const threadUpdate: FirebaseFirestore.UpdateData<FirebaseFirestore.DocumentData> = {
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    };

    if (moderationResult.decision !== "block") {
      threadUpdate.lastMessage = {
        text: combinedText,
        senderId: userId,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        messageId: newMessageRef.id,
      };
    }

    // Use a batch to update the thread and clean up
    const batch = db.batch();
    
    // Update the thread
    const threadRef = db.collection("threads").doc(threadId);
    batch.update(threadRef, threadUpdate);
    
    // Clean up buffered messages and flush request
    bufferSnapshot.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });
    batch.delete(snap.ref); // Delete the flush request

    await batch.commit();

    logger.info("Successfully processed and flushed message buffer", {
      threadId,
      userId,
      numMessages: bufferSnapshot.size,
      combinedTextLength: combinedText.length,
      decision: moderationResult.decision,
      latency: Date.now() - startTime,
    });
  },
);
