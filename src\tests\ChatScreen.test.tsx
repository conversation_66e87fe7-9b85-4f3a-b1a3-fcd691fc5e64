import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import ChatScreen from '../screens/ChatScreen';

// Mock react-native-gifted-chat
jest.mock('react-native-gifted-chat', () => ({
  GiftedChat: ({ onSend, messages, user }: any) => {
    const { View, Text, TouchableOpacity } = require('react-native');
    return (
      <View testID="gifted-chat">
        <Text testID="user-id">{user._id}</Text>
        <Text testID="messages-count">{messages.length}</Text>
        <TouchableOpacity
          testID="send-button"
          onPress={() => onSend([{ _id: 'test-message', text: 'Test message', createdAt: new Date(), user }])}
        >
          <Text>Send</Text>
        </TouchableOpacity>
      </View>
    );
  },
  Bubble: ({ children }: any) => children,
  Send: ({ children }: any) => children,
}));

// Mock useAuth hook
const mockChatUser = {
  uid: 'test-user-id',
  email: '<EMAIL>',
  displayName: 'Test User',
  createdAt: new Date().toISOString(),
  lastSeen: new Date().toISOString(),
};

jest.mock('../hooks/useAuth', () => ({
  useAuth: () => ({
    chatUser: mockChatUser,
  }),
}));

// Mock Firestore services
const mockSendMessage = jest.fn();
const mockSubscribeToThreadMessages = jest.fn();

jest.mock('../services/firestore', () => ({
  sendMessage: mockSendMessage,
  subscribeToThreadMessages: mockSubscribeToThreadMessages,
}));

// Mock navigation
const mockNavigation = {
  setOptions: jest.fn(),
};

const mockRoute = {
  params: {
    threadId: 'test-thread-id',
    threadTitle: 'Test Chat',
  },
};

describe('ChatScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Default mock for subscription
    mockSubscribeToThreadMessages.mockImplementation((threadId, callback) => {
      // Simulate initial empty messages
      callback([]);
      return jest.fn(); // unsubscribe function
    });
  });

  it('should render correctly with chat user', () => {
    const { getByTestId } = render(
      <ChatScreen route={mockRoute} navigation={mockNavigation} />
    );

    expect(getByTestId('gifted-chat')).toBeTruthy();
    expect(getByTestId('user-id')).toHaveTextContent('test-user-id');
  });

  it('should set navigation title from route params', () => {
    render(
      <ChatScreen route={mockRoute} navigation={mockNavigation} />
    );

    expect(mockNavigation.setOptions).toHaveBeenCalledWith({
      title: 'Test Chat',
    });
  });

  it('should subscribe to thread messages on mount', () => {
    render(
      <ChatScreen route={mockRoute} navigation={mockNavigation} />
    );

    expect(mockSubscribeToThreadMessages).toHaveBeenCalledWith(
      'test-thread-id',
      expect.any(Function)
    );
  });

  it('should display messages from Firestore', async () => {
    const mockMessages = [
      {
        id: 'msg-1',
        text: 'Hello world',
        timestamp: new Date().toISOString(),
        senderId: 'other-user',
        flagged: false,
      },
      {
        id: 'msg-2',
        text: 'How are you?',
        timestamp: new Date().toISOString(),
        senderId: 'test-user-id',
        flagged: false,
      },
    ];

    mockSubscribeToThreadMessages.mockImplementation((threadId, callback) => {
      callback(mockMessages);
      return jest.fn();
    });

    const { getByTestId } = render(
      <ChatScreen route={mockRoute} navigation={mockNavigation} />
    );

    await waitFor(() => {
      expect(getByTestId('messages-count')).toHaveTextContent('2');
    });
  });

  it('should display blocked message placeholder for flagged messages', async () => {
    const mockMessages = [
      {
        id: 'msg-1',
        text: 'Original message',
        timestamp: new Date().toISOString(),
        senderId: 'other-user',
        flagged: true,
      },
    ];

    mockSubscribeToThreadMessages.mockImplementation((threadId, callback) => {
      callback(mockMessages);
      return jest.fn();
    });

    render(
      <ChatScreen route={mockRoute} navigation={mockNavigation} />
    );

    // The component should convert flagged messages to blocked text
    // This would be verified in the actual GiftedChat component
    expect(mockSubscribeToThreadMessages).toHaveBeenCalled();
  });

  it('should send message when onSend is called', async () => {
    mockSendMessage.mockResolvedValue('new-message-id');

    const { getByTestId } = render(
      <ChatScreen route={mockRoute} navigation={mockNavigation} />
    );

    fireEvent.press(getByTestId('send-button'));

    await waitFor(() => {
      expect(mockSendMessage).toHaveBeenCalledWith(
        'test-thread-id',
        'test-user-id',
        'Test message'
      );
    });
  });

  it('should handle send message error', async () => {
    mockSendMessage.mockRejectedValue(new Error('Network error'));

    // Mock Alert
    const mockAlert = jest.fn();
    jest.doMock('react-native', () => ({
      ...jest.requireActual('react-native'),
      Alert: {
        alert: mockAlert,
      },
    }));

    const { getByTestId } = render(
      <ChatScreen route={mockRoute} navigation={mockNavigation} />
    );

    fireEvent.press(getByTestId('send-button'));

    await waitFor(() => {
      expect(mockSendMessage).toHaveBeenCalled();
    });
  });

  it('should show error when user is not authenticated', () => {
    // Mock useAuth to return no user
    jest.doMock('../hooks/useAuth', () => ({
      useAuth: () => ({
        chatUser: null,
      }),
    }));

    const ChatScreenWithNoUser = require('../screens/ChatScreen').default;

    const { getByText } = render(
      <ChatScreenWithNoUser route={mockRoute} navigation={mockNavigation} />
    );

    expect(getByText('Please sign in to access chat')).toBeTruthy();
  });

  it('should unsubscribe from messages on unmount', () => {
    const mockUnsubscribe = jest.fn();
    mockSubscribeToThreadMessages.mockReturnValue(mockUnsubscribe);

    const { unmount } = render(
      <ChatScreen route={mockRoute} navigation={mockNavigation} />
    );

    unmount();

    expect(mockUnsubscribe).toHaveBeenCalled();
  });

  it('should not send empty messages', async () => {
    const { getByTestId } = render(
      <ChatScreen route={mockRoute} navigation={mockNavigation} />
    );

    // Mock onSend with empty text
    const giftedChat = getByTestId('gifted-chat');
    
    // This would be tested with actual GiftedChat component
    // For now, we verify that sendMessage is not called with empty text
    expect(mockSendMessage).not.toHaveBeenCalledWith(
      expect.any(String),
      expect.any(String),
      ''
    );
  });
});
