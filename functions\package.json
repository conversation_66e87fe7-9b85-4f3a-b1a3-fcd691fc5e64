{"name": "civility-chat-functions", "version": "1.0.0", "description": "Firebase Cloud Functions for Civility Chat moderation", "main": "lib/index.js", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "dependencies": {"firebase-admin": "^12.0.0", "firebase-functions": "^5.0.0", "@google/generative-ai": "^0.21.0"}, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^20.0.0"}, "private": true}