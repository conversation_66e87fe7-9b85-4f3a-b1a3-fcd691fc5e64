# Civility Chat — Robust Moderation Strategy (Co‑Parenting Context)

> **Version:** Draft v1.0   |   **Author:** ChatGPT (o3)   |   **Last updated:** 2025‑07‑29
>
> **Purpose:** Support *civil, child‑focused* communication between divorced / separated parents by detecting, nudging, or blocking language that violates the community standards defined in our **Co‑Parenting Moderation Prompt**.

---

## 1. Design Goals

| Goal                                 | Rationale                                                                                                              |
| ------------------------------------ | ---------------------------------------------------------------------------------------------------------------------- |
| **Protect co‑parents & children**    | Intercept threats, manipulation, severe abuse, or repeated passive‑aggression that damages the parenting relationship. |
| **Guide users back on‑track**        | Provide *warn* nudges for tone issues ("passive‑aggressive", "hostility") instead of hard blocks when possible.        |
| **Low latency & cost**               | Most messages are mundane scheduling details; keep moderation budget ≈ \$0.50 / M msgs.                                |
| **Transparency without rule‑gaming** | Give minimal feedback: *allowed / softened / blocked* + short reason; never expose list of banned words.               |
| **Extensibility**                    | Trie, embeddings, or human escalation can be layered on later.                                                         |

---

## 2. Moderation Taxonomy & Decisions

| Decision  | Action                                                                                       | Example Triggers                                                                      |
| --------- | -------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------- |
| **allow** | Deliver immediately.                                                                         | "Pick‑up is at 3 pm today."                                                           |
| **warn**  | Deliver *or* soft‑rewrite (optional); show sender nudge *"Please re‑phrase more neutrally"*. | Mild insults ("idiot"), sarcastic digs, passive‑aggressive tone.                      |
| **block** | Drop message, increment strike counter, show block toast.                                    | Threats, profanity directed at co‑parent, manipulative guilt trip, severe harassment. |

**Labels** returned by the LLM (non‑exclusive): `threat`, `abuse`, `harassment`, `profanity`, `manipulation`, `hostility`, `passive‑aggressive`, `inappropriate-tone`, `personal-attack`, `clean`.

---

## 3. Pipeline Overview

```mermaid
flowchart LR
    IN(User Msg) --> Pre[L≤2 chars?]
    Pre -->|no| MainBuf
    Pre -->|yes| ShortGate

    ShortGate -->|allowable short| MainBuf
    ShortGate -->|warn| SoftNudge
    ShortGate -->|nonsense quota hit| TempMute

    MainBuf --> Flush{Idle ≥8 s OR ≥250 chars}
    Flush --> Primary[Gemini Moderate(tail+buf)]
    Primary -->|allow| Deliver
    Primary -->|warn| SoftNudge2
    Primary -->|block| Attr[Gemini(buf‑only)]
    Attr -->|block| HardBlock
    Attr -->|allow| ComboBlock

    Deliver --> TailUpdate
```

---

## 4. Parameters (Defaults)

| Name         | Value         | Notes                                                                                  |
| ------------ | ------------- | -------------------------------------------------------------------------------------- |
| `L_idle`     | **8 s**       | Flush after user pauses ≥ 8 s.                                                         |
| `L_max`      | **250 chars** | Hard flush.                                                                            |
| `TAIL_LEN`   | **30 chars**  | Last *delivered* text only; **blocked or muted messages are never added to this tail** |
| `sense T_hi` | **0.65**      | Short makes sense.                                                                     |
| `sense T_lo` | **0.35**      | Short is nonsense.                                                                     |
| `CN_LIMIT`   | **4/60 s**    | Temp‑mute if exceeded.                                                                 |
| `Mute_Δ`     | 30 s (×2^n)   | Exponential back‑off.                                                                  |

---

## 5. Logic Details

### 5.1 Short‑Message Gate (≤ 2 chars)

- **isProbablySensible(msg)** – heuristic list (`ok`, `hi`, `👍`, `?`, etc.).
- If *not* sensible → `GeminiSenseScore(context, msg)`.
  - `score ≥ 0.65` ⇒ treat as sensible.
  - `0.35–0.65` ⇒ **warn** (nudge: "Could you clarify?")
  - `< 0.35` ⇒ count toward nonsense quota; drop silently.

### 5.2 Primary & Attribution Checks

1. **Join text**: `joined = deliveredTail + candidate`.
2. **GeminiModerate(joined)** using **Co‑Parent Prompt**.
   - Returns `decision`, `labels`, `confidence`.
3. **Outcomes**
   - `decision = allow` ⇒ deliver & slide tail.
   - `decision = warn` ⇒ deliver (or rewrite) & nudge.
   - `decision = block` ⇒ run **Attribution** on `candidate` alone.
     - If second call blocks ⇒ *HardBlock* (reason: `new-text`).
     - Else ⇒ *ComboBlock* (reason: `combo`).

> **ComboBlock copy**: *"Message blocked because—combined with your previous wording—it violated community standards."*

### 5.3 Hard Block & Temp Mute

- Increment strike counter (`strikes +=1`).
- Reset `deliveredTail` to empty.
- temp‑mute `min(30 s × 2^strikes, 15 min)`.

---

## 6. API Schemas

### 6.1 `GeminiModerate(text)`

```jsonc
{
  "contents": [{"role": "user", "content": MODERATION_PROMPT.replace("{MESSAGE_TEXT}", text)}],
  "model": "gemini-2.5-flash-lite",
  "temperature": 0,
  "maxTokens": 64
}
```

### 6.2 Return Object

```jsonc
{
  "decision": "allow" | "warn" | "block",
  "labels": ["threat", …, "clean"],
  "confidence": 0.0‑1.0,
  "reasoning": "<32 chars>"
}
```

---

## 7. Cost Projection (w/ Co‑Parent taxonomy)

| Path           | Freq. | Avg Tokens | Cost/msg        |
| -------------- | ----- | ---------- | --------------- |
| Clean long     | 80 %  | 0          | \$0             |
| Short sensible | 15 %  | 0          | \$0             |
| Sense probe    | 3 %   | 25         | \$0.000003      |
| Primary join   | 2 %   | 60         | \$0.000015      |
| Attribution    | 0.3 % | 40         | \$0.000001      |
| **Blended**    | —     | ≈ 3        | **\$0.0000008** |

---

## 8. Reference Prompts

### 8.1 Co‑Parent MODERATION\_PROMPT (template)

```js
const MODERATION_PROMPT = `
You are a content moderator for a co-parenting communication app.
Your job is to analyze messages between divorced or separated parents
and determine if they contain toxic, abusive, or inappropriate content.

Analyze the following message and respond with ONLY a valid JSON object
in this exact format:
{
  "decision": "allow" | "warn" | "block",
  "labels": ["label1", "label2"],
  "confidence": 0.0-1.0,
  "reasoning": "brief explanation"
}

Decision criteria:
- "block": threats, severe abuse, profanity directed at a person, manipulation.
- "warn": mild hostility, passive‑aggression, minor name‑calling.
- "allow": professional, neutral, or positive child‑related comms.

Label guidelines:
• "profanity" only for explicit swear words.
• insults w/o swears ⇒ "hostility" or "personal-attack".

Possible labels: ["threat", "abuse", "harassment", "profanity",
"manipulation", "hostility", "passive-aggressive", "inappropriate-tone",
"personal-attack", "clean"]

Message to analyze: "{MESSAGE_TEXT}"
`;
```

### 8.2 Sense‑Score Prompt

```
You are a chat‑quality assistant. Given recent context and a user message,
return only a float 0‑1 indicating how *meaningful* the new message is.
0 = nonsense / spam, 1 = clearly meaningful.
CONTEXT:
{{context}}
MESSAGE:
"{{msg}}"
```

---

## 9. Logging Metrics (co‑parent focus)

| Metric                     | Use                                               |
| -------------------------- | ------------------------------------------------- |
| `decision_total{decision}` | Track allow/warn/block ratios.                    |
| `label_total{label}`       | Spot trends (e.g., rise in "passive‑aggressive"). |
| `strikes{uid}`             | Escalate repeat offenders.                        |
| `mute_duration_hist`       | UX impact.                                        |

---

## 10. Roadmap

1. **Inline rewrites** – auto‑replace mild insults (“jerk”) with neutral placeholders when *warn*.
2. **Shared calendar extraction** – detect if a message is only dates/locations & bypass heavy checks.
3. **Trie profanity filter** – lexical pre‑cut to drop token cost further.
4. **Human review queue** – surface contentious blocks (confidence < 0.4 & decision ≠ allow).

