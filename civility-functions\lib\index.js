"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.processMessageBuffer = exports.moderateMessage = void 0;
const firestore_1 = require("firebase-functions/v2/firestore");
const generative_ai_1 = require("@google/generative-ai");
const firebase_functions_1 = require("firebase-functions");
const crypto = __importStar(require("crypto"));
const admin = __importStar(require("firebase-admin"));
// Initialize Firebase Admin SDK
if (!admin.apps.length) {
    admin.initializeApp();
}
// Co-parenting specific moderation prompt
const MODERATION_PROMPT = `
You are a content moderator for a co-parenting communication app. 
Your job is to analyze messages between divorced or separated parents 
and determine if they contain toxic, abusive, or inappropriate content.

Analyze the following message and respond with ONLY a valid JSON object 
in this exact format:
{
  "decision": "allow" | "warn" | "block",
  "labels": ["label1", "label2"],
  "confidence": 0.0-1.0,
  "reasoning": "brief explanation"
}

Decision criteria:
- "block": Contains threats, severe abuse, harassment, profanity (explicit swear words like fuck, shit, asshole, bitch, etc.) directed at person, manipulation tactics
- "warn": Contains mild hostility, passive-aggressive language, inappropriate tone, name-calling or minor insults (e.g. "idiot", "dirtbag", "jerk")
- "allow": Professional, neutral, or positive communication about child-related matters

Label guidelines:
• Use "profanity" only when the text includes explicit swear words (fuck, shit, asshole, bitch, etc.)
• If the text contains insults or name-calling but no swear words, use "hostility" or "personal-attack" (not "profanity")
• Examples: "idiot", "dirtbag", "jerk", "loser" = "hostility" or "personal-attack", NOT "profanity"

Possible labels: ["threat", "abuse", "harassment", "profanity", 
"manipulation", "hostility", "passive-aggressive", "inappropriate-tone", 
"personal-attack", "clean"]

Message to analyze: "{MESSAGE_TEXT}"
`;
/**
 * Calls the Gemini API to moderate a given text.
 * @param {string} text The text to moderate.
 * @return {Promise<ModerationResponse>} The moderation result.
 */
async function getModerationResult(text) {
    const genAI = new generative_ai_1.GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    const model = genAI.getGenerativeModel({
        model: "gemini-1.5-flash-latest",
        generationConfig: {
            temperature: 0.1,
            topK: 1,
            topP: 0.8,
            maxOutputTokens: 200,
            responseMimeType: "application/json",
        },
    });
    const prompt = MODERATION_PROMPT.replace("{MESSAGE_TEXT}", text);
    try {
        const result = await model.generateContent(prompt);
        const response = await result.response;
        const responseText = response.text();
        const moderationResult = JSON.parse(responseText);
        // Validate response structure
        if (!moderationResult.decision ||
            !["allow", "warn", "block"].includes(moderationResult.decision)) {
            firebase_functions_1.logger.error("Invalid moderation decision from API", {
                decision: moderationResult.decision,
            });
            moderationResult.decision = "allow"; // Safe fallback
        }
        return moderationResult;
    }
    catch (error) {
        firebase_functions_1.logger.error("Failed to get or parse Gemini response", {
            error,
        });
        // Fallback to allow if API call or parsing fails
        return {
            decision: "allow",
            labels: ["moderation-error"],
            confidence: 0.0,
            reasoning: "Failed to get or parse moderation response",
        };
    }
}
exports.moderateMessage = (0, firestore_1.onDocumentCreated)({
    document: "chats/{threadId}/messages/{msgId}",
    timeoutSeconds: 10,
    memory: "256MiB",
    region: "northamerica-northeast1",
}, async (event) => {
    var _a;
    const startTime = Date.now();
    const snap = event.data;
    if (!snap) {
        firebase_functions_1.logger.error("No document snapshot provided");
        return;
    }
    const messageData = snap.data();
    const { text, senderId } = messageData;
    if (!text || typeof text !== "string") {
        firebase_functions_1.logger.warn("No text content to moderate", { messageId: snap.id });
        return;
    }
    // Skip if already moderated
    if (messageData.moderated) {
        firebase_functions_1.logger.info("Message already moderated", { messageId: snap.id });
        return;
    }
    firebase_functions_1.logger.info("Starting moderation", {
        messageId: snap.id,
        textLength: text.length,
        senderId,
    });
    try {
        const moderationResult = await getModerationResult(text);
        firebase_functions_1.logger.info("Moderation result received", {
            messageId: snap.id,
            decision: moderationResult.decision,
            latency: Date.now() - startTime,
        });
        // Create message hash for audit trail (without storing plaintext)
        const messageHash = crypto.createHash("sha256").update(text).digest("hex");
        // Prepare update data with structured moderation object
        const updateData = {
            moderated: true,
            moderatedAt: new Date(),
            messageHash,
            latency: Date.now() - startTime,
            visible: true, // Always visible - UI handles display based on moderation decision
            moderation: {
                decision: moderationResult.decision,
                confidence: moderationResult.confidence || 0.0,
                labels: moderationResult.labels || [],
                reason: moderationResult.reasoning || "AI moderation decision",
                timestamp: new Date().toISOString(),
            },
        };
        // Handle blocked messages
        if (moderationResult.decision === "block") {
            updateData.originalText = text; // Store original text
            updateData.text = "🛑 Message removed by moderation";
            updateData.flagged = true;
            updateData.blocked = true;
            firebase_functions_1.logger.warn("Message blocked", {
                messageId: snap.id,
                labels: moderationResult.labels,
                confidence: moderationResult.confidence,
            });
        }
        else if (moderationResult.decision === "warn") {
            updateData.warned = true;
            firebase_functions_1.logger.info("Message warned", {
                messageId: snap.id,
                labels: moderationResult.labels,
                confidence: moderationResult.confidence,
            });
        }
        // Add reasoning if provided (for debugging)
        if (moderationResult.reasoning) {
            updateData.reasoning = moderationResult.reasoning;
        }
        // Update the document
        await snap.ref.update(updateData);
        // Update thread's lastMessage only for non-blocked messages
        const threadId = (_a = snap.ref.parent.parent) === null || _a === void 0 ? void 0 : _a.id;
        const shouldUpdateThread = moderationResult.decision !== "block"; // Skip blocked messages in thread preview
        firebase_functions_1.logger.info("Attempting to update thread lastMessage", {
            messageId: snap.id,
            threadId,
            decision: moderationResult.decision,
            shouldUpdateThread,
        });
        if (threadId && shouldUpdateThread) {
            try {
                const db = admin.firestore();
                const threadRef = db.doc(`threads/${threadId}`);
                // Check if thread document exists first
                const threadDoc = await threadRef.get();
                if (!threadDoc.exists) {
                    // Create thread document if it doesn't exist
                    firebase_functions_1.logger.info("Creating missing thread document", {
                        messageId: snap.id,
                        threadId,
                    });
                    await threadRef.set({
                        participants: [messageData.senderId], // Will need to add other participant
                        createdAt: admin.firestore.FieldValue.serverTimestamp(),
                        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
                        lastMessage: {
                            text: text, // Use original text for thread preview
                            senderId: messageData.senderId,
                            timestamp: messageData.timestamp,
                            visible: true,
                        },
                    });
                }
                else {
                    // Update existing thread
                    await threadRef.update({
                        lastMessage: {
                            text: text, // Use original text for thread preview
                            senderId: messageData.senderId,
                            timestamp: messageData.timestamp,
                            visible: true,
                        },
                        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
                    });
                }
                firebase_functions_1.logger.info("Successfully updated thread lastMessage", {
                    messageId: snap.id,
                    threadId,
                    created: !threadDoc.exists,
                });
            }
            catch (threadUpdateError) {
                firebase_functions_1.logger.error("Failed to update thread lastMessage", {
                    messageId: snap.id,
                    threadId,
                    error: threadUpdateError instanceof Error ? threadUpdateError.message : String(threadUpdateError),
                });
            }
        }
        else if (threadId && !shouldUpdateThread) {
            firebase_functions_1.logger.info("Skipping thread lastMessage update for blocked message", {
                messageId: snap.id,
                threadId,
                decision: moderationResult.decision,
            });
        }
        const totalLatency = Date.now() - startTime;
        firebase_functions_1.logger.info("Moderation completed", {
            messageId: snap.id,
            decision: moderationResult.decision,
            totalLatency,
            success: true,
        });
        // Log performance metrics
        if (totalLatency > 400) {
            firebase_functions_1.logger.warn("Moderation latency exceeded target", {
                messageId: snap.id,
                latency: totalLatency,
                target: 400,
            });
        }
    }
    catch (error) {
        const totalLatency = Date.now() - startTime;
        firebase_functions_1.logger.error("Moderation failed", {
            messageId: snap.id,
            error: error instanceof Error ? error.message : String(error),
            latency: totalLatency,
        });
        // Update with error state but don't block the message
        await snap.ref.update({
            moderated: true,
            moderatedAt: new Date(),
            decision: "allow", // Fail open for safety
            error: error instanceof Error ? error.message : String(error),
            latency: totalLatency,
        });
    }
});
exports.processMessageBuffer = (0, firestore_1.onDocumentCreated)({
    document: "threads/{threadId}/flushRequests/{userId}",
    timeoutSeconds: 60,
    memory: "256MiB",
    region: "northamerica-northeast1",
}, async (event) => {
    const startTime = Date.now();
    const snap = event.data;
    if (!snap) {
        firebase_functions_1.logger.error("No flush request data provided");
        return;
    }
    const { threadId, userId } = event.params;
    firebase_functions_1.logger.info("Processing message buffer flush request", {
        threadId,
        userId,
    });
    const db = admin.firestore();
    const bufferRef = db.collection(`chats/${threadId}/buffers/${userId}/messages`);
    const bufferSnapshot = await bufferRef.orderBy("timestamp").get();
    if (bufferSnapshot.empty) {
        firebase_functions_1.logger.warn("Buffer is empty, deleting flush request.", { threadId, userId });
        await snap.ref.delete();
        return;
    }
    // Concatenate messages
    const combinedText = bufferSnapshot.docs
        .map((doc) => doc.data().text)
        .join(" ")
        .trim();
    if (!combinedText) {
        firebase_functions_1.logger.warn("Buffer contained only empty messages.", { threadId, userId });
        // Clean up buffer and request even if text is empty
        const batch = db.batch();
        bufferSnapshot.docs.forEach((doc) => {
            batch.delete(doc.ref);
        });
        batch.delete(snap.ref);
        await batch.commit();
        return;
    }
    // Moderate the combined text
    const moderationResult = await getModerationResult(combinedText);
    // Create a new message in the main chat thread
    const mainMessagesRef = db.collection(`chats/${threadId}/messages`);
    const newMessageRef = mainMessagesRef.doc();
    const newMessageData = {
        text: combinedText,
        senderId: userId,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        moderated: true,
        moderatedAt: new Date(),
        visible: true, // UI will handle visibility based on moderation
        status: "sent",
        moderation: {
            decision: moderationResult.decision,
            confidence: moderationResult.confidence || 0.0,
            labels: moderationResult.labels || [],
            reason: moderationResult.reasoning || "AI moderation decision",
            timestamp: new Date().toISOString(),
            source: "buffer",
        },
    };
    // Handle blocked messages from buffer
    if (moderationResult.decision === "block") {
        newMessageData.originalText = combinedText;
        newMessageData.text = "🛑 Message removed by moderation";
        newMessageData.flagged = true;
        newMessageData.blocked = true;
    }
    // First, save the new message
    await newMessageRef.set(newMessageData);
    // Only update thread lastMessage for non-blocked messages
    const threadUpdate = {
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    };
    if (moderationResult.decision !== "block") {
        threadUpdate.lastMessage = {
            text: combinedText,
            senderId: userId,
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
            messageId: newMessageRef.id,
        };
    }
    // Use a batch to update the thread and clean up
    const batch = db.batch();
    // Update the thread
    const threadRef = db.collection("threads").doc(threadId);
    batch.update(threadRef, threadUpdate);
    // Clean up buffered messages and flush request
    bufferSnapshot.docs.forEach((doc) => {
        batch.delete(doc.ref);
    });
    batch.delete(snap.ref); // Delete the flush request
    await batch.commit();
    firebase_functions_1.logger.info("Successfully processed and flushed message buffer", {
        threadId,
        userId,
        numMessages: bufferSnapshot.size,
        combinedTextLength: combinedText.length,
        decision: moderationResult.decision,
        latency: Date.now() - startTime,
    });
});
//# sourceMappingURL=index.js.map