import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  RefreshControl,
  Modal,
  TextInput,
  Button,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '../hooks/useAuth';
import { subscribeToUserThreads, createChatThread, getUser, getUserByEmail } from '../services/firestore';
import { ChatThread } from '../types/chat';

interface ChatListScreenProps {
  navigation: any;
}

interface ChatListItem extends ChatThread {
  otherParticipantName?: string;
}

export default function ChatListScreen({ navigation }: ChatListScreenProps) {
  const { user } = useAuth();
  const [threads, setThreads] = useState<ChatListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [newChatEmail, setNewChatEmail] = useState('');

  // Subscribe to user's chat threads
  useEffect(() => {
    if (!user?.uid) return;

    const unsubscribe = subscribeToUserThreads(
      user.uid,
      async (userThreads: ChatThread[]) => {
        // Enhance threads with participant names
        const enhancedThreads = await Promise.all(
          userThreads.map(async (thread) => {
            // Find the other participant (not the current user)
            const otherParticipantId = thread.participants.find(
              (id) => id !== user.uid
            );

            let otherParticipantName = 'Unknown User';
            if (otherParticipantId) {
              try {
                const otherUser = await getUser(otherParticipantId);
                otherParticipantName = otherUser?.displayName || otherUser?.email || 'Unknown User';
              } catch (error) {
                console.error('Error fetching participant name:', error);
              }
            }

            return {
              ...thread,
              otherParticipantName,
            };
          })
        );

        setThreads(enhancedThreads);
        setLoading(false);
        setRefreshing(false);
      }
    );

    return unsubscribe;
  }, [user?.uid]);

  const onRefresh = () => {
    setRefreshing(true);
    // The subscription will automatically refresh the data
  };

  const createNewChat = () => {
    setModalVisible(true);
  };

  const handleCreateChat = async () => {
    if (!newChatEmail?.trim() || !user?.uid) return;
    try {
      setLoading(true);
      // Look up recipient by email
      const recipientUser = await getUserByEmail(newChatEmail.trim());
      if (!recipientUser) {
        Alert.alert('Error', 'No user found with that email.');
        setLoading(false);
        return;
      }
      const threadId = await createChatThread([user.uid, recipientUser.uid]);
      setModalVisible(false);
      setNewChatEmail('');
      navigation.navigate('Chat', {
        threadId,
        threadTitle: recipientUser.displayName || recipientUser.email,
      });
    } catch (error) {
      console.error('Error creating chat:', error);
      Alert.alert('Error', 'Failed to create chat. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const navigateToChat = (thread: ChatListItem) => {
    navigation.navigate('Chat', {
      threadId: thread.id,
      threadTitle: thread.otherParticipantName || 'Chat',
    });
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const getPreviewText = (thread: ChatListItem, currentUserId: string) => {
    if (!thread.lastMessage) return 'No messages yet';
    
    const message = thread.lastMessage;
    
    // With moderation-first architecture, only visible messages should be in lastMessage
    // But check for safety
    if (!message.visible) {
      return 'Message pending...';
    }
    
    const isWarned = message.moderation?.decision === 'warn' || message.warned;
    
    if (isWarned) {
      // Show the actual text for warned messages but with indicator
      return `⚠️ ${message.text}`;
    } else {
      // Show normal text for allowed messages
      return message.text;
    }
  };

  const renderChatItem = ({ item }: { item: ChatListItem }) => (
    <TouchableOpacity
      style={styles.chatItem}
      onPress={() => navigateToChat(item)}
    >
      <View style={styles.avatarContainer}>
        <Text style={styles.avatarText}>
          {item.otherParticipantName?.charAt(0).toUpperCase() || '?'}
        </Text>
      </View>
      
      <View style={styles.chatInfo}>
        <View style={styles.chatHeader}>
          <Text style={styles.chatName} numberOfLines={1}>
            {item.otherParticipantName || 'Unknown User'}
          </Text>
          {item.lastMessage && (
            <Text style={styles.timestamp}>
              {formatTimestamp(item.lastMessage.timestamp)}
            </Text>
          )}
        </View>
        
        <Text style={styles.lastMessage} numberOfLines={2}>
          {getPreviewText(item, user?.uid || '')}
        </Text>
      </View>
    </TouchableOpacity>
  );

  if (!user) {
    return (
      <SafeAreaView style={styles.container}>
        <Text style={styles.errorText}>Please sign in to access chats</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Chats</Text>
        <TouchableOpacity style={styles.newChatButton} onPress={createNewChat}>
          <Text style={styles.newChatButtonText}>+ New Chat</Text>
        </TouchableOpacity>
      </View>

      {loading && threads.length === 0 ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading chats...</Text>
        </View>
      ) : (
        <FlatList
          data={threads}
          renderItem={renderChatItem}
          keyExtractor={(item) => item.id}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>No chats yet</Text>
              <Text style={styles.emptySubtext}>
                Tap "New Chat" to start a conversation
              </Text>
            </View>
          }
          contentContainerStyle={threads.length === 0 ? styles.emptyList : undefined}
        />
      )}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.centeredView}>
          <View style={styles.modalView}>
            <Text style={styles.modalText}>Enter the email address of the person you want to chat with:</Text>
            <TextInput
              style={styles.input}
              value={newChatEmail}
              onChangeText={setNewChatEmail}
              placeholder="Email address"
              keyboardType="email-address"
              autoCapitalize="none"
            />
            <View style={styles.buttonContainer}>
              <Button title="Cancel" onPress={() => {
                setModalVisible(false);
                setNewChatEmail('');
              }} />
              <Button title="Create" onPress={handleCreateChat} />
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  newChatButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  newChatButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  chatItem: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  avatarContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  chatInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  chatName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  timestamp: {
    fontSize: 12,
    color: '#666',
    marginLeft: 8,
  },
  lastMessage: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyList: {
    flex: 1,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 50,
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)'
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    width: '80%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    elevation: 5
  },
  modalText: {
    marginBottom: 15,
    textAlign: 'center'
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    padding: 10,
    borderRadius: 5,
    marginBottom: 15
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  }
});
