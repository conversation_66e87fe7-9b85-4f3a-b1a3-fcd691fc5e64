import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { useAuth } from '../hooks/useAuth';
import { subscribeToUserThreads, createChatThread, getUser } from '../services/firestore';
import { ChatThread } from '../types/chat';

interface ChatListScreenProps {
  navigation: any;
}

interface ChatListItem extends ChatThread {
  otherParticipantName?: string;
}

export default function ChatListScreen({ navigation }: ChatListScreenProps) {
  const { user } = useAuth();
  const [threads, setThreads] = useState<ChatListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Subscribe to user's chat threads
  useEffect(() => {
    if (!user?.uid) return;

    const unsubscribe = subscribeToUserThreads(
      user.uid,
      async (userThreads: ChatThread[]) => {
        // Enhance threads with participant names
        const enhancedThreads = await Promise.all(
          userThreads.map(async (thread) => {
            // Find the other participant (not the current user)
            const otherParticipantId = thread.participants.find(
              (id) => id !== user.uid
            );

            let otherParticipantName = 'Unknown User';
            if (otherParticipantId) {
              try {
                const otherUser = await getUser(otherParticipantId);
                otherParticipantName = otherUser?.displayName || otherUser?.email || 'Unknown User';
              } catch (error) {
                console.error('Error fetching participant name:', error);
              }
            }

            return {
              ...thread,
              otherParticipantName,
            };
          })
        );

        setThreads(enhancedThreads);
        setLoading(false);
        setRefreshing(false);
      }
    );

    return unsubscribe;
  }, [user?.uid]);

  const onRefresh = () => {
    setRefreshing(true);
    // The subscription will automatically refresh the data
  };

  const createNewChat = () => {
    Alert.prompt(
      'New Chat',
      'Enter the email address of the person you want to chat with:',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Create',
          onPress: async (email) => {
            if (!email?.trim() || !user?.uid) return;

            try {
              setLoading(true);
              
              // For now, create a thread with a placeholder participant
              // In a real app, you'd look up the user by email first
              const threadId = await createChatThread([user.uid, email.trim()]);
              
              navigation.navigate('Chat', {
                threadId,
                threadTitle: email.trim(),
              });
            } catch (error) {
              console.error('Error creating chat:', error);
              Alert.alert('Error', 'Failed to create chat. Please try again.');
            } finally {
              setLoading(false);
            }
          },
        },
      ],
      'plain-text'
    );
  };

  const navigateToChat = (thread: ChatListItem) => {
    navigation.navigate('Chat', {
      threadId: thread.id,
      threadTitle: thread.otherParticipantName || 'Chat',
    });
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const renderChatItem = ({ item }: { item: ChatListItem }) => (
    <TouchableOpacity
      style={styles.chatItem}
      onPress={() => navigateToChat(item)}
    >
      <View style={styles.avatarContainer}>
        <Text style={styles.avatarText}>
          {item.otherParticipantName?.charAt(0).toUpperCase() || '?'}
        </Text>
      </View>
      
      <View style={styles.chatInfo}>
        <View style={styles.chatHeader}>
          <Text style={styles.chatName} numberOfLines={1}>
            {item.otherParticipantName || 'Unknown User'}
          </Text>
          {item.lastMessage && (
            <Text style={styles.timestamp}>
              {formatTimestamp(item.lastMessage.timestamp)}
            </Text>
          )}
        </View>
        
        <Text style={styles.lastMessage} numberOfLines={2}>
          {item.lastMessage?.text || 'No messages yet'}
        </Text>
      </View>
    </TouchableOpacity>
  );

  if (!user) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Please sign in to access chats</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Chats</Text>
        <TouchableOpacity style={styles.newChatButton} onPress={createNewChat}>
          <Text style={styles.newChatButtonText}>+ New Chat</Text>
        </TouchableOpacity>
      </View>

      {loading && threads.length === 0 ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading chats...</Text>
        </View>
      ) : (
        <FlatList
          data={threads}
          renderItem={renderChatItem}
          keyExtractor={(item) => item.id}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>No chats yet</Text>
              <Text style={styles.emptySubtext}>
                Tap "New Chat" to start a conversation
              </Text>
            </View>
          }
          contentContainerStyle={threads.length === 0 ? styles.emptyList : undefined}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  newChatButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  newChatButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  chatItem: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  avatarContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  chatInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  chatName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  timestamp: {
    fontSize: 12,
    color: '#666',
    marginLeft: 8,
  },
  lastMessage: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyList: {
    flex: 1,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 50,
  },
});
