import React, { useState, useEffect, useCallback, useRef } from 'react';
import { View, StyleSheet, Alert, Text, TouchableOpacity, Pressable } from 'react-native';
import { GiftedChat, IMessage as GiftedChatMessage, User as GiftedChatUser, Send, Bubble, MessageText } from 'react-native-gifted-chat';
import { useAuth } from '../hooks/useAuth';
import { sendMessage, subscribeToThreadMessages, bufferMessage, flushBuffer } from '../services/firestore';
import { Message, MessageStatus } from '../types/chat';

// Extend the IMessage interface to include our custom status field
interface IMessage extends GiftedChatMessage {
  status?: MessageStatus;
  isBlocked?: boolean;
  isWarned?: boolean;
  isRevealed?: boolean;
  isCurrentUser?: boolean;
  originalText?: string;
  moderation?: Message['moderation'];
}

interface ChatScreenProps {
  route: {
    params: {
      threadId: string;
      threadTitle?: string;
    };
  };
  navigation: any;
}

export default function ChatScreen({ route, navigation }: ChatScreenProps) {
  const { threadId, threadTitle } = route.params;
  const { user } = useAuth();
  const [messages, setMessages] = useState<IMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [revealedMessages, setRevealedMessages] = useState<Set<string>>(new Set());
  const flushTimer = useRef<NodeJS.Timeout | null>(null);

  // Set navigation title
  useEffect(() => {
    if (threadTitle) {
      navigation.setOptions({ title: threadTitle });
    }
  }, [navigation, threadTitle]);

  // Subscribe to messages
  useEffect(() => {
    if (!threadId) return;

    const unsubscribe = subscribeToThreadMessages(
      threadId,
      (firestoreMessages: Message[]) => {
        console.log('DEBUG: Received messages from Firestore:', firestoreMessages.length);
        firestoreMessages.forEach(msg => {
          console.log('Message:', {
            id: msg.id,
            text: msg.text,
            moderated: msg.moderated,
            visible: msg.visible,
            decision: msg.moderation?.decision
          });
        });
        
        // Filter messages: show own messages immediately, others after moderation
        const visibleMessages = firestoreMessages.filter(msg => 
          msg.senderId === user?.uid || (msg.moderated && msg.visible)
        );
        
        console.log('DEBUG: Visible messages after filter:', visibleMessages.length);
        
        // Convert visible Firestore messages to GiftedChat format
        const giftedMessages: IMessage[] = visibleMessages.map((msg) => {
          const isCurrentUser = msg.senderId === user?.uid;
          const isBlocked = msg.moderation?.decision === 'block' || msg.flagged;
          const isWarned = msg.moderation?.decision === 'warn' || msg.warned;
          const isRevealed = revealedMessages.has(msg.id);
          
          // Determine display text based on moderation status
          let displayText = msg.text;
          if (isBlocked && !isRevealed) {
            displayText = isCurrentUser 
              ? '🛑 Your message was blocked. Tap to see original and rephrase.'
              : '🛑 Message removed by moderation. Tap to reveal.';
          }
          
          return {
            _id: msg.id,
            text: displayText,
            createdAt: new Date(msg.timestamp),
            user: {
              _id: msg.senderId,
              name: msg.senderId === user?.uid ? 'You' : 'User',
            },
            // Add custom properties for moderation
            pending: false,
            system: false,
            // Custom moderation properties
            moderation: msg.moderation,
            originalText: msg.originalText || msg.text,
            isBlocked,
            isWarned,
            isRevealed,
            isCurrentUser,
          };
        });

        setMessages(giftedMessages);
        setLoading(false);
      }
    );

    return unsubscribe;
  }, [threadId, user?.uid, revealedMessages]);

  useEffect(() => {
    // Clear the timer when the component unmounts
    return () => {
      if (flushTimer.current) {
        clearTimeout(flushTimer.current);
      }
    };
  }, []);

  const onSend = useCallback(async (newMessages: IMessage[] = []) => {
    if (!user || !threadId) {
      Alert.alert('Error', 'Unable to send message. Please try again.');
      return;
    }

    const message = newMessages[0];
    if (!message?.text?.trim()) return;

    const text = message.text.trim();
    const SHORT_MESSAGE_THRESHOLD = 3;

    // Optimistically add message to UI
    const tempMessage: IMessage = {
      _id: `temp-${Date.now()}`,
      text: text,
      createdAt: new Date(),
      user: { _id: user.uid },
      status: 'sent',
    };

    setMessages((previousMessages) =>
      GiftedChat.append(previousMessages, [tempMessage])
    );

    try {
      if (text.length < SHORT_MESSAGE_THRESHOLD) {
        // Buffer short messages
        console.log('Buffering short message:', text);
        await bufferMessage(threadId, user.uid, text);

        // Reset the flush timer
        if (flushTimer.current) {
          clearTimeout(flushTimer.current);
        }
        flushTimer.current = setTimeout(async () => {
          console.log('Timer triggered - flushing buffer for user:', user.uid);
          try {
            await flushBuffer(threadId, user.uid);
            console.log('Flush buffer completed successfully');
          } catch (error) {
            console.error('Error flushing buffer from timer:', error);
          }
        }, 8000); // 8-second idle timer

      } else {
        // Flush any pending short messages and send the long one
        console.log('Sending long message, flushing buffer first');
        if (flushTimer.current) {
          clearTimeout(flushTimer.current);
        }
        await flushBuffer(threadId, user.uid);
        console.log('Buffer flushed, now sending long message');
        await sendMessage(threadId, user.uid, text);
        console.log('Long message sent successfully');
      }
    } catch (error) {
      console.error('Error in message sending/buffering:', error);
      Alert.alert('Error', 'Failed to send message. Please try again.');
    }
  }, [user, threadId]);

  const renderSend = (props: any) => {
    console.log('renderSend props:', props);
    return (
      <Send {...props} containerStyle={{ justifyContent: 'center' }}>
        <View style={styles.sendButton}>
          <Text style={styles.sendButtonText}>Send</Text>
        </View>
      </Send>
    );
  };

  const handleRevealMessage = useCallback((messageId: string) => {
    console.log('Revealing message:', messageId);
    setRevealedMessages(prev => {
      const newSet = new Set([...prev, messageId]);
      console.log('Updated revealed messages:', Array.from(newSet));
      return newSet;
    });
  }, []);

  const renderMessageStatus = (message: IMessage) => {
    if (message.user._id !== user?.uid) return null;

    let statusText = '';
    switch ((message as IMessage).status) {
      case 'sent':
        statusText = '✓';
        break;
      case 'delivered':
        statusText = '✓✓';
        break;
      case 'read':
        statusText = '✓✓'; // Could use a different color for read
        break;
      case 'failed':
        statusText = '✗';
        break;
    }

    return (
      <Text style={{ fontSize: 10, color: '#999', marginRight: 10 }}>
        {statusText}
      </Text>
    );
  };

  const renderBubble = (props: any) => {
    const message = props.currentMessage;
    const isBlocked = message?.moderation?.decision === 'block' || message?.flagged;
    const isWarned = message?.moderation?.decision === 'warn' || message?.warned;
    const isRevealed = revealedMessages.has(message?._id);
    const isCurrentUser = message?.user?._id === user?.uid;
    
    // Debug logging for blocked messages
    if (isBlocked) {
      console.log('Blocked message debug:', {
        messageId: message?._id,
        isBlocked,
        isRevealed,
        hasOriginalText: !!message?.originalText,
        moderation: message?.moderation,
        flagged: message?.flagged,
        revealedMessages: Array.from(revealedMessages)
      });
    }
    
    // Custom wrapper style based on moderation status
    const wrapperStyle = {
      right: {
        backgroundColor: isBlocked ? '#ff6b6b' : isWarned ? '#ffa726' : '#007AFF',
      },
      left: {
        backgroundColor: isBlocked ? '#ffebee' : isWarned ? '#fff3e0' : '#f0f0f0',
      },
    };
    
    const textStyle = {
      right: {
        color: '#fff',
      },
      left: {
        color: isBlocked || isWarned ? '#333' : '#000',
      },
    };
    
    return (
      <View style={{ flexDirection: 'row', alignItems: 'flex-end' }}>
        {/* For current user, status is on the left of the bubble */}
        {isCurrentUser && renderMessageStatus(props.currentMessage)}
        <Bubble
          {...props}
          wrapperStyle={wrapperStyle}
          textStyle={textStyle}
          renderMessageText={(textProps) => (
            <View>
              {isBlocked && !isRevealed ? (
                <TouchableOpacity 
                  onPress={() => handleRevealMessage(message._id)}
                  style={styles.blockedMessageContainer}
                >
                  <Text style={styles.blockedMessageText}>
                    {isCurrentUser ? '🛑 Your message was blocked' : '🛑 Message removed by moderation'}
                  </Text>
                  <Text style={styles.tapToRevealText}>
                    {isCurrentUser ? 'Tap to see original and rephrase' : 'Tap to reveal'}
                  </Text>
                </TouchableOpacity>
              ) : isBlocked && isRevealed ? (
                <View>
                  <Text style={{ color: isCurrentUser ? '#fff' : '#333', fontWeight: 'normal' }}>
                    {message.originalText || 'Original message not available'}
                  </Text>
                </View>
              ) : (
                <MessageText {...textProps} />
              )}
              
              {/* Show moderation info for revealed blocked messages */}
              {isBlocked && isRevealed && (
                <View style={styles.moderationInfo}>
                  <Text style={styles.moderationInfoText}>
                    🛑 Blocked: {message.moderation?.reason || 'Inappropriate content detected'}
                  </Text>
                  {isCurrentUser && (
                    <Text style={styles.rephraseHint}>
                      Consider rephrasing your message to be more constructive.
                    </Text>
                  )}
                </View>
              )}
              
              {/* Show warning banner for warned messages */}
              {isWarned && (
                <View style={styles.warningBanner}>
                  <Text style={styles.warningText}>
                    ⚠️ This message may contain inappropriate language
                  </Text>
                  {message.moderation?.labels && (
                    <Text style={styles.warningLabels}>
                      Detected: {message.moderation.labels.join(', ')}
                    </Text>
                  )}
                </View>
              )}
            </View>
          )}
        />
      </View>
    );
  };

  if (!user) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Please sign in to access chat</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <GiftedChat
        key={`chat-${threadId}`}
        messages={messages}
        onSend={onSend}
        user={{
          _id: user.uid,
          name: user.displayName || 'You',
        }}
        renderSend={renderSend}
        renderBubble={renderBubble}
        placeholder="Type a message..."
        alwaysShowSend
        scrollToBottomComponent={() => (
          <View style={styles.scrollToBottomButton}>
            <Text style={styles.scrollToBottomText}>↓</Text>
          </View>
        )}
        isLoadingEarlier={loading}
        renderLoadEarlier={() => null}
        infiniteScroll={false}
        keyboardShouldPersistTaps="never"
        textInputProps={{
          autoCorrect: true,
          autoCapitalize: 'sentences',
          multiline: true,
          maxLength: 1000,
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  sendButton: {
    backgroundColor: '#007AFF',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    marginBottom: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
  scrollToBottomButton: {
    backgroundColor: '#007AFF',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
    marginRight: 10,
  },
  scrollToBottomText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 50,
  },
  // Moderation UI styles
  blockedMessageContainer: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  blockedMessageText: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  tapToRevealText: {
    fontSize: 12,
    opacity: 0.8,
    fontStyle: 'italic',
  },
  moderationInfo: {
    marginTop: 8,
    padding: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 6,
    borderLeftWidth: 3,
    borderLeftColor: '#ff6b6b',
  },
  moderationInfoText: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  rephraseHint: {
    fontSize: 11,
    opacity: 0.9,
    fontStyle: 'italic',
  },
  warningBanner: {
    marginTop: 6,
    padding: 8,
    backgroundColor: 'rgba(255, 193, 7, 0.1)',
    borderRadius: 6,
    borderLeftWidth: 3,
    borderLeftColor: '#ffa726',
  },
  warningText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#f57c00',
    marginBottom: 2,
  },
  warningLabels: {
    fontSize: 11,
    color: '#f57c00',
    opacity: 0.8,
  },
});
