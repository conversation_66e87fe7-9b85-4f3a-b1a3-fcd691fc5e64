import React, { useState, useEffect, useCallback } from 'react';
import { View, StyleSheet, Alert, Text } from 'react-native';
import { GiftedChat, IMessage, User as GiftedChatUser, Send, Bubble } from 'react-native-gifted-chat';
import { useAuth } from '../hooks/useAuth';
import { sendMessage, subscribeToThreadMessages } from '../services/firestore';
import { Message } from '../types/chat';

interface ChatScreenProps {
  route: {
    params: {
      threadId: string;
      threadTitle?: string;
    };
  };
  navigation: any;
}

export default function ChatScreen({ route, navigation }: ChatScreenProps) {
  const { threadId, threadTitle } = route.params;
  const { user } = useAuth();
  const [messages, setMessages] = useState<IMessage[]>([]);
  const [loading, setLoading] = useState(true);

  // Set navigation title
  useEffect(() => {
    if (threadTitle) {
      navigation.setOptions({ title: threadTitle });
    }
  }, [navigation, threadTitle]);

  // Subscribe to messages
  useEffect(() => {
    if (!threadId) return;

    const unsubscribe = subscribeToThreadMessages(
      threadId,
      (firestoreMessages: Message[]) => {
        // Convert Firestore messages to GiftedChat format
        const giftedMessages: IMessage[] = firestoreMessages.map((msg) => ({
          _id: msg.id,
          text: msg.flagged ? '🛑 This message was blocked by moderation' : msg.text,
          createdAt: new Date(msg.timestamp),
          user: {
            _id: msg.senderId,
            name: msg.senderId === user?.uid ? 'You' : 'User',
          },
          // Add custom properties for moderation
          pending: false,
          system: msg.flagged || false,
        }));

        setMessages(giftedMessages);
        setLoading(false);
      }
    );

    return unsubscribe;
  }, [threadId, user?.uid]);

  const onSend = useCallback(async (newMessages: IMessage[] = []) => {
    if (!user || !threadId) {
      Alert.alert('Error', 'Unable to send message. Please try again.');
      return;
    }

    const message = newMessages[0];
    if (!message?.text?.trim()) return;

    try {
      // Add message to Firestore
      await sendMessage(threadId, user.uid, message.text.trim());
    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert('Error', 'Failed to send message. Please try again.');
    }
  }, [user, threadId]);

  const renderSend = (props: any) => (
    <Send {...props}>
      <View style={styles.sendButton}>
        <Text style={styles.sendButtonText}>Send</Text>
      </View>
    </Send>
  );

  const renderBubble = (props: any) => {
    const isSystem = props.currentMessage?.system;
    
    return (
      <Bubble
        {...props}
        wrapperStyle={{
          right: {
            backgroundColor: isSystem ? '#ff6b6b' : '#007AFF',
          },
          left: {
            backgroundColor: isSystem ? '#ff6b6b' : '#f0f0f0',
          },
        }}
        textStyle={{
          right: {
            color: '#fff',
          },
          left: {
            color: isSystem ? '#fff' : '#000',
          },
        }}
      />
    );
  };

  if (!user) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Please sign in to access chat</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <GiftedChat
        messages={messages}
        onSend={onSend}
        user={{
          _id: user.uid,
          name: user.displayName || 'You',
        }}
        renderSend={renderSend}
        renderBubble={renderBubble}
        placeholder="Type a message..."
        alwaysShowSend
        scrollToBottomComponent={() => (
          <View style={styles.scrollToBottomButton}>
            <Text style={styles.scrollToBottomText}>↓</Text>
          </View>
        )}
        isLoadingEarlier={loading}
        renderLoadEarlier={() => null}
        infiniteScroll={false}
        keyboardShouldPersistTaps="never"
        textInputProps={{
          autoCorrect: true,
          autoCapitalize: 'sentences',
          multiline: true,
          maxLength: 1000,
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  sendButton: {
    backgroundColor: '#007AFF',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    marginBottom: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
  scrollToBottomButton: {
    backgroundColor: '#007AFF',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
    marginRight: 10,
  },
  scrollToBottomText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 50,
  },
});
